import xlsx from "json-as-xlsx"
import { assetsMgr } from "./AssetsMgr"
import Battle from "./Battle"
import { BattleRoleType } from "./BattleEnum"
import BattleRole from "./BattleRole"
import PassengerModel from "./PassengerModel"
import { cfgHelper } from "./cfgHelper"
import { util } from "./utils/Utils"
import { ChapterPlanetMonsterCfg, CharacterCfg, PlanetMonsterCfg, TowerMonsterCfg } from "./constant/DataType"
import Monster from "./Monster"
import { PlanetNodeType, TeamId } from "./constant/Enums"

require("./extend/ExtendArray")
assetsMgr.init()
cfgHelper.init()

const args = process.argv.slice(2).filter(arg => !arg.includes("--"))

const monsterJson = "TowerMonster"
const startIndex = "1-1"
const endIndex = "1-5"

const passengerJson = "BattleTeamsTest30"

let genBattleTest = async () => {
    let diffLv = args[1] || 0
    diffLv = Number(diffLv)
    console.log("等级差: ", diffLv)

    let testData: BattleTeamsCfg[] = assetsMgr.getJson(passengerJson).datas
    let planetNodes = assetsMgr.getJson("PlanetNodes").datas
    let planetDatas: ChapterPlanetMonsterCfg[] = assetsMgr.getJson(monsterJson).datas

    const LV = 120, STAR_LV = 4
    let monsterTeams = planetDatas.map((data) => {
        let team = data.monster.map(info => {
            let lv = LV + diffLv
            let starLv = STAR_LV
            let role = new Monster().init(info.id, lv, starLv)
            let r = new BattleRole().initData({ id: role.id, hp: role.getHp(), attack: role.getAttack(), skills: role.getSkills(), lv, starLv})
            r.mergeSkills()
            return r
        })
        return { id: data.id, hero: data["hero"], team }
    })

    let round = 0
    let handleBattle = (passengers, monsters) => {
        // let now = Date.now()
        // passengers = passengers.map(role => new BattleRole().initData({ id: role.id, hp: role.hp, attack: role.attack, skills: role.skills, lv: role.lv}))
        // monsters = monsters.map(role => new BattleRole().initData({ id: role.id, hp: role.hp, attack: role.attack, skills: role.skills, lv: role.lv}))

        passengers = passengers.map(role => {
            role.type = BattleRoleType.PASSENGER
            return role.reset()
        })
        monsters = monsters.map(role => {
            role.type = BattleRoleType.MONSTER
            return role.reset()
        })
        try {
            let battle = new Battle()
            battle.openDebug = false
            battle.openLog = false
            let isWin = battle.init(passengers, monsters)
            // console.log("cost: ", Date.now() - now, battle.round)
            round += battle["round"]
            return [battle.passengers.length, battle.monsters.length, battle["round"]]
        } catch (error) {
            console.error(error)
            console.log(passengers.map(p => {
                return {id: p.id, lv: p.lv, starLv: p.starLv}
            }))
            console.log(monsters.map(p => {
                return {id: p.id, lv: p.lv, starLv: p.starLv}
            }))
        }
 
    }

    let count = 0
  
    let result = []

    let roleMap = {}
    let testTeams = testData.map(t => {
        return t.monster.map(id=>{
            if (id > 2000) {
                let cfg = cfgHelper.getCharacter(id)
                return cfg.battleSkill || -1
            }
            return id
        })
    }).filter(monsters => !monsters.has(-1))

    // for (let monster of testTeams) {
    //     for (let id of monster) {
    //         if (!roleMap[id]) roleMap[id] = 0
    //         roleMap[id]++
    //     }
    // }
    // console.log(roleMap)
    // for (let id in roleMap) {
    //     let name = cfgHelper.getCharacter(Number(id)).name
    //     name = assetsMgr.lang(name)
    //     result.push({name, cnt: roleMap[id]})
    // }
    // result.sort((a, b)=>{
    //     return b.cnt - a.cnt
    // })
    // console.log(result.length, testTeams.length)
    // console.log(result)
    // return

    let go2 = async (monsters) => {
        // console.log("testTeamId: ", id)
        let win = 0
        for (let data of testTeams) {
            let lv = LV
            let starLv = STAR_LV
            let passengers = data.map((id, index) => {
                if (!roleMap[id]) roleMap[id] = {}
                if (!roleMap[id][lv]) roleMap[id][lv] = {}
                let r = roleMap[id][lv][starLv]
                if (!r) {
                    let role = new PassengerModel().init({ id, level: lv, starLv })
                    r = new BattleRole().initData({ id: role.getID(), hp: role.getHp(), attack: role.getAttack(), skills: role.getSkills() })
                    r.mergeSkills()
                    roleMap[id][lv][starLv] = r
                }
                return r
            })
            let [pCnt, mCnt, round] = handleBattle(passengers, monsters)
            if (pCnt > mCnt) {
                win++
            }
            // else {
            //     console.log(passengers.map(p => p.id))
            //     console.log(monsters.map(p => p.id))
            // }
            count++
            if (count > 10000) {
                count = 0
                await util.wait(50)
            }
        }
        return win / testTeams.length
    }

    let transToLine = (index) => {
        // let idx = 0
        // for (let i = 0; i < planetNodes.length; i++) {
        //     let node = planetNodes[i]
        //     if (node.type == PlanetNodeType.CHECK_POINT) {
        //         idx++
        //     }
        //     if (idx == index) {
        //         return i+3
        //     }
        // }
        return index + 3
    }
    
    let isStart = false
    let outs = { "行": [], "关卡ID": [], "胜率": [] }
    
    for (let i = 0; i < monsterTeams.length; i++) {
        let team = monsterTeams[i]
        if (team.id == startIndex) {
            isStart = true
        }
        if (!isStart) continue
        let win = await go2(team.team)
        let line = transToLine(i+1)
        console.log(line, team.id, "胜率: ", util.toFixed(win, 2))
        outs["行"].push(line)
        outs["关卡ID"].push(team.id)
        outs["胜率"].push(util.toFixed(win, 2))
        if (endIndex == team.id) break
    }

    toExcel(outs)
}

genBattleTest()

let toExcel = (data, fileName?) => {
    let sheet = { columns: [], content: [] }
    let keyMap = {}
    let flat = (obj, path, res) => {
        if (typeof obj == 'object' && obj != null) {
            if (Array.isArray(obj)) {
                for (let val of obj) {
                    flat(val, path, res)
                }
            }
            else {

                for (let key in obj) {
                    if (path != "") {
                        flat(obj[key], `${path}-${key}`, res)
                    }
                    else {
                        flat(obj[key], `${key}`, res)
                    }
                }
            }
        }
        else {
            keyMap[path] = path
            res[path] = obj
        }
    }
    let keys = Object.keys(data)
    let count = data[keys[0]].length
    for (let i = 0; i < count; i++) {
        let res = {}
        for (let key in data) {
            flat(data[key][i], key, res)
        }
        sheet.content.push(res)
    }

    sheet.columns = Object.keys(keyMap).sort((key1, key2) => {
        return keys.findIndex(k => key1.includes(k)) - keys.findIndex(k => key2.includes(k))
    }).map(key => {
        return { label: key, value: key }
    })
    let settings = {
        fileName: !!fileName ? fileName : "./output/ChapterMonsterTest", // Name of the resulting spreadsheet
        extraLength: 3, // A bigger number means that columns will be wider
        writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional. Useful in such cases https://docs.sheetjs.com/docs/solutions/output#example-remote-file
        writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
    }
    xlsx([sheet], settings)
}
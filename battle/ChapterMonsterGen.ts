import xlsx from "json-as-xlsx"
import { assetsMgr } from "./AssetsMgr"
import Battle from "./Battle"
import { BattleRoleType } from "./BattleEnum"
import BattleRole from "./BattleRole"
import PassengerModel from "./PassengerModel"
import { cfgHelper } from "./cfgHelper"
import { util } from "./utils/Utils"
import { ChapterPlanetMonsterCfg, CharacterCfg, PlanetMonsterCfg, TowerMonsterCfg } from "./constant/DataType"
import Monster from "./Monster"
import { exit } from "process"

require("./extend/ExtendArray")
assetsMgr.init()
cfgHelper.init()
let outs = {}

const START_INDEX = "1006-3-4"
const TEST_COUNT = 1000

let genBattleTest = async () => {
    let teamMap = {}

    let hasTeam = (team: any[]) => {
        let id = getTeamKey(team)
        return !!teamMap[id]
    }

    let addTeam = (team: any[]) => {
        let id = getTeamKey(team)
        teamMap[id] = true
    }

    let getTeamKey = (team: any[]) => {
        team = team.slice().sort((a, b) => { return a - b})
        return team.reduce((pre, id) => `${pre}_${id}`, "")
    }

    let characterQulityMap = {}
    let monsterDatas: PlanetMonsterCfg[] = assetsMgr.getJson("PlanetMonster").datas
    let characterDatas: CharacterCfg[] = assetsMgr.getJson("Character").datas
    let testData: BattleTeamsCfg[] = assetsMgr.getJson("BattleTeamsTest30").datas
    let planetDatas: ChapterPlanetMonsterCfg[] = assetsMgr.getJson("ChapterPlanetMonster").datas

    for (let i = 1; i <= 3; i++) {
        characterQulityMap[i] = characterDatas.filter(c => c.quality == i).map(c => c.id)
    }

    let cntMap = {}
    for (let data of planetDatas) {
        if (data.id == START_INDEX) break
        for (let monster of data.monster) {
            let cfg = assetsMgr.getJsonData("PlanetMonster", monster.id)
            if (!cfg) {
                console.log(monster.id)
            }
            if (cfg.isBoss || cfg.isSp || cfg.id == 2004) continue
            let id = monster.id
            if (!cntMap[id]) cntMap[id] = 0
            cntMap[id]++
        }
    }

    let round = 0
    let handleBattle = (passengers, monsters) => {
        // let now = Date.now()
        // passengers = passengers.map(role => new BattleRole().initData({ id: role.id, hp: role.hp, attack: role.attack, skills: role.skills, lv: role.lv}))
        // monsters = monsters.map(role => new BattleRole().initData({ id: role.id, hp: role.hp, attack: role.attack, skills: role.skills, lv: role.lv}))

        passengers = passengers.map(role => {
            role.type = BattleRoleType.PASSENGER
            return role.reset()
        })
        monsters = monsters.map(role => {
            role.type = BattleRoleType.MONSTER
            return role.reset()
        })
        try {
            let battle = new Battle()
            battle.openDebug = false
            battle.openLog = false
            let isWin = battle.init(passengers, monsters)
            // console.log("cost: ", Date.now() - now, battle.round)
            round += battle["round"]
            return [battle.passengers.length, battle.monsters.length, battle["round"]]
        } catch (error) {
            console.error(error)
            console.log(passengers.map(p => {
                return {id: p.id, lv: p.lv, starLv: p.starLv}
            }))
            console.log(monsters.map(p => {
                return {id: p.id, lv: p.lv, starLv: p.starLv}
            }))
        }
 
    }

    let count = 0
    let result = []
    let roleMap = {}

    let lv = 120, starLv = 4
    let go2 = async (monsters, id) => {
        let testTeams = testData.map(t => t.monster)
        console.log("testTeamId: ", id)
        let win = 0
        for (let data of testTeams) {
            let passengers = data.map(id => {
                if (!roleMap[id]) roleMap[id] = {}
                if (!roleMap[id][lv]) roleMap[id][lv] = {}
                let r = roleMap[id][lv][starLv]
                if (!r) {
                    let role = new PassengerModel().init({ id, level: lv, starLv })
                    r = new BattleRole().initData({ id: role.getID(), hp: role.getHp(), attack: role.getAttack(), skills: role.getSkills(), role })
                    r.mergeSkills()
                    roleMap[id][lv][starLv] = r
                }
                return r
            })
            let [pCnt, mCnt, round] = handleBattle(passengers, monsters)
            if (pCnt > mCnt) {
                win++
            }
            count++
            if (count > 10000) {
                count = 0
                await util.wait(50)
            }
        }
        win = win / testTeams.length
        if (win < 0.9) {
            result.push({ monsters: monsters, win})
        }
    }
    
    let newNames = []
    let newList = newNames.map(name => {
        return monsterDatas.find(m => assetsMgr.lang(m.name) == name).id
    })

    let genTeam = (cfg?: ChapterPlanetMonsterCfg) => {
        // let id = cfg.id
        // let [_1, _2, index] = id.split("-")
        let isDiffcult = util.chance(20)
        let teamIds = util.randomArray(newList.slice())
        if (!isDiffcult) {
            teamIds = teamIds.slice(0, 1)
        }

        let getWeight = (id) => {
            return cntMap[id] * 2
        }
        let randomAry = []
        let maxWeight = 0
        for (let id in cntMap) {
            maxWeight = Math.max(maxWeight, getWeight(id))
        }
        for (let id in cntMap) {
            randomAry.push({id: Number(id), weight: maxWeight + 1 - getWeight(id)})
        }
        let count = 5 - teamIds.length
        for (let i = 0; i < count; i++) {
            let index = util.randomByWeight(randomAry)
            teamIds.push(randomAry[index].id)
            randomAry.splice(index, 1)
        }
        util.randomArray(teamIds)
        return teamIds
    }


    for (let i = 0; i < TEST_COUNT; i++) {
        let teamIds = genTeam()
        let team = teamIds.map(id => {
            let role = new Monster().init(id, lv, starLv)
            let r = new BattleRole().initData({ id: role.id, hp: role.getHp(), attack: role.getAttack(), skills: role.getSkills(), lv: role.lv, starLv: role.getStarLv() })
            r.mergeSkills()
            r["name"] = assetsMgr.lang(role["json"].name)
            return r
        })
        await go2(team, i)
    }

    result.sort((a, b)=>{
        return b.win - a.win
    })

    // let groups = []
    // while (1) {
    //     let group = []
    //     let cfgs = [
    //         {win: [10, 20], count: 12},
    //         {win: [25, 35], count: 4},
    //         {win: [45], count: 4},
    //     ]

    //     for (let data of result) {
    //         let win = data.win
    //         let cfg =  cfgs.find(c => c.win[0] <= win && win <= c.win[1] && c.count > 0)
    //         if (cfg) {
    //             cfg.count--
    //             group.push(data)
    //         }
    //     }
    // }


    outs["胜率"] = []
    for (let {monsters, win} of result) {
        monsters.forEach((monster, i)=>{
            let key = "怪物" + i
            if (!outs[key]) outs[key] = []
            outs[key].push(monster.name)
        })
        outs["胜率"].push(win)
    }

    // for (let data of planetDatas) {
    //     if (data.id != startIndex) continue
    //     let startTime = Date.now()
     
    //     console.log(data.id, " cost: ", Date.now() - startTime, "胜率: ", result[result.length - 1].win)
    //     if (data.id == endIndex) break
    // }

    toExcel(outs)
}

let toExcel = (data, fileName?) => {
    let sheet = { columns: [], content: [] }
    let keyMap = {}
    let flat = (obj, path, res) => {
        if (typeof obj == 'object' && obj != null) {
            if (Array.isArray(obj)) {
                for (let val of obj) {
                    flat(val, path, res)
                }
            }
            else {

                for (let key in obj) {
                    if (path != "") {
                        flat(obj[key], `${path}-${key}`, res)
                    }
                    else {
                        flat(obj[key], `${key}`, res)
                    }
                }
            }
        }
        else {
            keyMap[path] = path
            res[path] = obj
        }
    }
    let keys = Object.keys(data)
    let count = data[keys[0]].length
    for (let i = 0; i < count; i++) {
        let res = {}
        for (let key in data) {
            flat(data[key][i], key, res)
        }
        sheet.content.push(res)
    }

    sheet.columns = Object.keys(keyMap).sort((key1, key2) => {
        return keys.findIndex(k => key1.includes(k)) - keys.findIndex(k => key2.includes(k))
    }).map(key => {
        return { label: key, value: key }
    })
    let settings = {
        fileName: !!fileName ? fileName : "./output/ChapterMonsterGen", // Name of the resulting spreadsheet
        extraLength: 3, // A bigger number means that columns will be wider
        writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional. Useful in such cases https://docs.sheetjs.com/docs/solutions/output#example-remote-file
        writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
    }
    xlsx([sheet], settings)
}

genBattleTest()
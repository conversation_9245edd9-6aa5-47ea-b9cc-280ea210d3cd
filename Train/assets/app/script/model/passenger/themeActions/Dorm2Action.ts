import { CarriageUsePosType, Dorm2BuildType, PassengerLifeAnimation } from "../../../common/constant/Enums"
import BuildObj from "../../train/common/BuildObj"
import { ActionNode } from "../ActionTree"
import { TimeStateData } from "../StateDataType"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"
import Dorm2Model from "../../train/dorm2/Dorm2Model"
import Dorm2TableObj from "../../train/dorm2/Dorm2TableObj"
import { ActionCfg } from "./ActionCfg"
import { gameHelper } from "../../../common/helper/GameHelper"

export default class Dorm2Action extends BaseAction {

    protected carriage: Dorm2Model = null

    protected async start(action: ActionNode) {
        this.debug('start')

        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (foodState) {
            action.onTerminate = () => {
                this.removeFood()
            }
            await action.run(this.toEat)
            return
        }

        await this.onBeforeStart(action)
        if (action.isOK()) return

        else {
            let cfgs = [
                { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
                { act: this.toRandomPos, weight: 25 }, //闲逛
                { act: this.toBuildPlay, weight: 75, check: this.checkPlay }, //和设施交互
            ]
            await this.runRandomAct(action, cfgs)
            await action.wait(ut.random(1, 3))
        }
    }

    private checkPlay() {
        return this.checkChairPlay() || this.checkHearthPlay() || this.checkTVPlay()
    }

    private checkChairPlay() {
        let builds = this.carriage.getChairs()
        return builds.some(b => this.checkBuildPlay(b))
    }

    private checkHearthPlay() {
        return this.checkToCook() || this.checkToTakeFood()
    }

    private checkToCook() {
        let hearth = this.carriage.getHearth()
        return this.checkBuildPlay(hearth) && !hearth.hasFood() && this.actionAgent.getAnim(PassengerLifeAnimation.COOK)
    }

    private checkToTakeFood() {
        let hearth = this.carriage.getHearth()
        if (!hearth || !hearth.hasFood()) return false
        return this.actionAgent.getAnim(PassengerLifeAnimation.WALK_PLATE)
    }

    private checkTVPlay() {
        return this.checkBuildPlay(this.carriage.getTV(), [0, 1])
    }

    //去睡觉
    protected async toSleep(action) {
        let bed = this.carriage.getBed()
        let cfgs = [
        ]
        let anim = this.actionAgent.getAnim(PassengerLifeAnimation.SLEEP)
        if (anim) {
            if (bed && bed.canUse()) {
                cfgs.push({ act: this.toBedSleep })
            }
            if (cfgs.length <= 0) {
                // let build = this.getUseChair(false)
                // if (build) {
                //     cfgs.push({ act: this.toChairSleep, params: { build } })
                // }
            }
        }

        if (cfgs.length <= 0) {
            cfgs.push({ act: this.lyingDownSleep, params: { anim: PassengerLifeAnimation.SLEEP } })
        }
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    protected async toEat(action) {
        if (this.getUseChair(false)) {
            await this.toChairEat(action)
        }
        else {
            await this.toStandEat(action)
        }
    }

    protected async toStandEat(action) {
        await action.run(this.toRandomPos)
        let anim = PassengerLifeAnimation.STAND_EAT_BY_PLATE
        let animMunch = PassengerLifeAnimation.STAND_MUNCH_BY_PLATE
        this.actionAgent.pushState(StateType.HOLD_TABLEWARE)
        await action.run(this.eat, { anim, animMunch })
        action.ok()
    }

    //去和设施交互
    protected async toBuildPlay(action) {
        let cfgs = [
            { act: this.toChairPlay, check: this.checkChairPlay, weight: 10 }, //去凳子交互
            { act: this.toHearthPlay, check: this.checkHearthPlay, weight: 10 }, //去厨房交互
            { act: this.toWatchTV, check: this.checkTVPlay, weight: 10 }, //去电视交互
        ]

        await this.runRandomAct(action, cfgs)
        action.ok()
    }


    //------------ bed --------------------------
    protected async toBedSleep(action) {
        let build = this.carriage.getBed()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
        let sleepParams = Object.assign({ build }, action.params)
        await action.run(this.sleep, sleepParams)
        action.onTerminate()
        action.ok()
    }

    //---------------------------------------------

    //-------------------- chair -----------------------------
    // 凳子交互
    protected async toChairPlay(action) {
        let build = this.getUseChair()
        let cfgs = [
            { act: this.toChairSit, weight: 40 }, //坐着
            { act: this.toChairEat, check: this.checkSitEat, weight: 10 }, //吃东西
            { act: this.toChairDrink, check: this.checkSitDrink, weight: 10 }, //喝饮料
            { act: this.toChairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs, { build })
        action.ok()
    }

    // 凳子睡觉
    protected async toChairSleep(action: ActionNode) {
        await this.toChairAct(action, this.sitSleep)
    }

    // 凳子吃东西
    protected async toChairEat(action) {
        let params = action.params || {}
        let actionAgent = this.actionAgent
        let foodState = actionAgent.getState(StateType.HOLD_FOOD)
        if (foodState) { //如果是端着食物，按顺序坐
            params.build = this.carriage.getChairs().filter(b => b.canUse())[0]
        }
        action.params = params
        await this.toChairAct(action, this.toSitEat)
    }

    protected async toSitEat(action) {
        let { build } = action.params
        let actionAgent = this.actionAgent
        let foodState = actionAgent.getState(StateType.HOLD_FOOD)
        let table = this.carriage.getTable() as Dorm2TableObj
        let index = -1
        if (build.type == Dorm2BuildType.CHAIR_1) {
            index = 0
        }
        else if (build.type == Dorm2BuildType.CHAIR_2) {
            index = 1
        }
        let useTable = foodState && table && index >= 0
        let anim = PassengerLifeAnimation.SIT_EAT, delayTime, holdFood
        let animMunch
        if (foodState) {
            actionAgent.pushState(StateType.HOLD_TABLEWARE)
            anim = PassengerLifeAnimation.SIT_EAT_BY_PLATE
            animMunch = PassengerLifeAnimation.SIT_MUNCH_BY_PLATE
            delayTime = -1
        }
        if (useTable) {
            anim = PassengerLifeAnimation.SIT_EAT_BY_TABLE
            animMunch = PassengerLifeAnimation.SIT_MUNCH_BY_TABLE
            table.addFood(index, foodState.data.food)
            this.removeFood()
            holdFood = false
        }
        action.onTerminate = () => {
            if (useTable) {
                table.removeFood(index)
            }
        }
        await action.run(this.sitEat, Object.assign(action.params, { eatAnim: anim, delayTime, holdFood, build, animMunch }))
        action.onTerminate()
        action.ok()
    }

    // 凳子喝饮料
    protected async toChairDrink(action) {
        await this.toChairAct(action, this.sitDrink)
    }

    // 凳子坐着
    protected async toChairSit(action) {
        await this.toChairAct(action, this.sit)
    }

    // 坐在凳子表演
    protected async toChairPlayAct(action) {
        await this.toChairAct(action, this.sitPlayAct)
    }

    protected async toChairAct(action, act) {
        let params = action.params || {}
        let { build } = params
        if (!build) {
            build = this.getUseChair()
        }
        if (!build) {
            console.warn("toChairAct not found", build)
            return action.ok()
        }

        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
        await action.run(act, Object.assign(params, { build }))
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, 0)
        action.ok()
    }

    protected getUseChair(isPlay = true) {
        return this.getUseBuild(this.carriage.getChairs(), isPlay)
    }
    //----------------------------------------------

    //------------------- Hearth -------------------
    // 灶台交互
    protected async toHearthPlay(action: ActionNode) {
        if (this.checkToCook()) {
            await action.run(this.toCook)
        }

        if (this.checkToTakeFood()) {
            await action.run(this.toTakeFood)
        }
        action.ok()
    }

    // 灶台做菜
    protected async toCook(action) {
        let { time } = action.params || {}
        let build = this.carriage.getHearth()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
            build.checkReset()
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
        await action.run(this.readyCook)
        await action.run(this.moveToBuild, { build, paths: [{ index: 1 }] })
        await action.run(this.cook, { build, time })
        this.actionAgent.addUseBuildRecord(build, index)
        action.onTerminate()
        action.ok()
    }

    //准备菜
    protected async readyCook(action) {
        let build = this.carriage.getHearth()
        let actionAgent = this.actionAgent
        await action.run(build.readyFood, null, build)
        action.onTerminate = () => {
            actionAgent.popState(StateType.COOK)
        }
        this.actionAgent.pushState(StateType.COOK, { anim: PassengerLifeAnimation.CUT_FOOD })
        await action.run(build.cutFood, null, build)
        action.onTerminate()
        action.ok()
    }

    //做菜
    protected async cook(action) {
        let { time } = action.params
        let build = this.carriage.getHearth()
        let actionAgent = this.actionAgent
        let type = StateType.COOK
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        time = time || this.randomTime(ActionCfg[type])
        this.actionAgent.pushState(type)
        await action.run(build.cook, { time }, build)
        action.onTerminate()
        action.ok()
    }

    //去拿菜
    protected async toTakeFood(action) {
        let build = this.carriage.getHearth()
        build.lockFood()
        action.onTerminate = () => {
            build.unlockFood()
        }
        await action.run(this.moveToBuild, { build, paths: [{ index: 1 }] })
        this.addFood({ food: "fried_rice", slot: "panzi" })
        build.takeFood()
        action.ok()
    }

    //-------------------------------------------------------

    //------------------ TV ---------------------------------
    //去看电视
    protected async toWatchTV(action) {
        let { time } = action.params || {}
        let build: BuildObj = null
        let isSit: boolean = true
        let index: number = 0
        // 乘客看电视时，行为优先级如下：坐在两个蓝色椅子上>坐在白色团垫上>站在电视两侧
        let chairs = this.carriage.getChairs().filter(b => b.canUse())
        let blue = chairs.filter(b => b.type != Dorm2BuildType.CHAIR_1)
        if (blue.length > 0) {
            build = blue.random()
        } else {
            let lazy = chairs.filter(b => b.type == Dorm2BuildType.CHAIR_1)
            if (lazy.length > 0) {
                build = lazy[0]
            } else {
                build = this.carriage.getTV()
                index = [0, 1].filter(i => build.canUse(i)).random()
                isSit = false
            }
        }
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
            this.actionAgent.popState(StateType.SIT)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        if (isSit) {
            this.actionAgent.pushState(StateType.SIT, { build })
        }
        await action.run(this.watchTV, { time, index })
        action.onTerminate()
        action.ok()
    }

    //看电视
    protected async watchTV(action) {
        let { time, index } = action.params
        let build = this.carriage.getTV()
        let actionAgent = this.actionAgent
        let type = StateType.WATCH_TV
        action.onTerminate = () => {
            actionAgent.popState(type)
            build.onExit(this.role)
        }
        build.onEnter(this.role)
        time = time || this.randomTime(ActionCfg[type])
        let ft = time
        let bt = 0
        const err = ut.random(0, 100) > 90
        if (err) {
            ft = ut.random(5, time - 5)
            bt = time - ft
        }
        let timeData = new TimeStateData().init(ft)
        this.actionAgent.pushState(type, { timeData, index })
        await action.wait(timeData)
        bt > 0 && build.onError()
        timeData = new TimeStateData().init(bt)
        await action.wait(timeData)
        this.actionAgent.addUseBuildRecord(build, -1)
        action.onTerminate()
        action.ok()
    }

    //-------------------------------------------------------


    //------------ 基础行为 -----------------------------------

    protected async lyingDownSleep(action: ActionNode) {
        // 走去能睡觉的地方
        const targetPos = this.carriage
            .getUsePosListByType(CarriageUsePosType.LYING_DOWN_SLEEP)
            .filter(pos => this.carriage.checkUsePos(pos.index)).random()
        if (!targetPos) {
            const arg = Object.assign({}, action.params)
            arg.anim = PassengerLifeAnimation.STAND_SLEEP
            await action.run(this.sleep, arg)
            action.ok()
            return
        }
        this.carriage.setUseLock(true, targetPos.index)
        await action.run(this.move, { pos: targetPos.pos })
        // 等待水填满车厢  进入loop动画后再开始睡觉
        await this.carriage.waterOnWaterLoop()
        let now = gameHelper.now()
        const data = {
            time: action.params.time,
            anim: action.params.anim,
            hideEffect: action.params.hideEffect,
            awakeTime: action.params.awakeTime,
            dir: action.params.dir,
            timeData: null
        }
        action.onTerminate = () => {
            this.carriage.setUseLock(false, targetPos.index)
            this.carriage.addAccTotal(this.role, (gameHelper.now() - now) / ut.Time.Hour)
            this.actionAgent.popState(StateType.FLOAT_SLEEP)
        }
        if (data.time) {
            let timeData = new TimeStateData().init(data.time)
            data.timeData = timeData
            this.actionAgent.pushState(StateType.FLOAT_SLEEP, data)
            await action.wait(timeData)
        }
        else {
            if (!data.awakeTime) {
                data.awakeTime = this.getAwakeTime()
            }
            this.actionAgent.pushState(StateType.FLOAT_SLEEP, data)
            await action.run(this.checkSleepEnd)
        }
        action.onTerminate()
        action.ok()
    }

}
enum StateType {
    // common
    NONE, // 空状态
    IDLE, //
    MOVE, //移动
    MOVE_STEP, //移动

    //1号寝室
    CHECK_IN,
    CHECK_IN_IDLE,
    CHECK_IN_MOVE,
    CHECK_IN_TIDY,
    ENTER_BED,
    ENTER_LEFT_BED_BY_STAIR,
    ENTER_RIGHT_BED_BY_BOOKCASE,
    SLEEP, //睡觉
    EXIT_BED,
    SIT,
    SIT_SLEEP,
    SIT_EAT,
    EAT,
    DRINK,
    ENTER_STAIR,
    EXIT_STAIR,
    ENTER_CHAIR,
    EXIT_CHAIR,
    PLAY_ACT,
    WANT_SLEEP, //想睡觉

    HOLD_FOOD, //拿着食物
    HOLD_TABLEWARE, //拿着餐具

    //二号寝室
    READY_COOK,
    COOK,
    WATCH_TV,
    USE_PLATE, //端着盘子
    FLOAT_SLEEP, // 漂浮睡觉

    //三号寝室
    WC_QUEUE,//排队厕所

    //车头
    FISH, // 钓鱼
    STARGAZE, //观星

    //引擎室
    WALK_TREADMILL, //跑步机上行走
    ENGINE_PULL, //使用拉力器
    ENGINE_COMPUTER, //使用电脑
    ENGINE_NOTES, //记笔记
    ENGINE_POWER_FIRST, //动力室-赏金猎犬-旋转阀门
    ENGINE_POWER_SECONE, //动力室-胡桃夹子喵-扳手修理
    ENGINE_ADD_FUEL, //往锅炉丢燃料

    //餐厅
    DINING_FOOD_QUEUE,//排队点餐机
    DINING_FOOD_PULL,//拉动点餐机
    DINING_DRINK_QUEUE,//排队饮料机
    DINING_DRINK_PULL,//拉动饮料机
    WANT_EAT, //想吃东西
    WANT_DRINK, //想和饮料

    //列车每日任务相关
    PATROL, //巡逻
    ON_REPAIR, //修理
    REPAIR, //修理
    OVERTIME, //加班
    ON_CLEAN, //打扫
    CLEAN, //打扫
    STAND, //站岗
    LOST_SEARCH, //失物搜寻
    INSPECT, //查寝
    CLOTHES_CLEAN, //清洗衣物
    SAFETY_PROMOTION, //安全宣传
    LIFE_GOODS, //发放生活用品

    //探索
    EXPLORE_BOX, //拿着探索宝箱
    PLOT, //角色剧情
    DAILY_TASK, //日常任务

    //生活技能
    COLLECT_STAR, //捡星尘
    COLLECT_HEART, //捡爱心

    //浴室
    TAKE_BATH,//泡澡
    FROG_QUEUE,//排队去青蛙领茶包

    //舞厅
    DANCING,//跳舞

    //摇摇椅相关
    ROCKING_CHAIR, //摇摇椅

    //造水间
    WATER_ADD, //加煤炭
    WATER_GAME, //玩游戏
    WATER_CRY, //玩游戏后哭哭
    WATER_USE_TABLE, //使用实验台
    WATER_RUN, //跑步
    WATER_AIR, //打气

    // 1号寝室小鸡
    BIRD_SLEEP, // 睡觉
    BIRD_BIAO_YAN_1, // 表演1 起飞
    BIRD_BIAO_YAN_2, // 表演2 乱叫
    BIRD_FLY, // 飞行
    BIRD_IDLE, // 空闲
}


export {
    StateType
}
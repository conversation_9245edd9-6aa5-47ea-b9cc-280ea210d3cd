import { cfg<PERSON>el<PERSON> } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class PvpRewardPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rankLbl_: cc.Label = null // path://root/rank_l
    protected dailyNode_: cc.Node = null // path://root/daily_n
    protected seasonNode_: cc.Node = null // path://root/season_n
    //@end
    _showArgs: { type: proto.PvpType } = null

    get info() {
        return gameHelper.pvp.getPvpInfo(this._showArgs.type)
    }

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false })
    }

    public onEnter(data: any) {
        this._showArgs = data
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://mask_be
    onClickMask(event: cc.Event.EventTouch, data: string) {
        this.hide()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    initView() {
        const info = this.info
        this.rankLbl_.setLocaleKey("pvp_guiText_13", info.rank)

        const rNum = this.getReward(info.rank)
        const seasonRNum = this.getSeasonReward(info.rank)
        this.setReward(this.dailyNode_, rNum)
        this.setReward(this.seasonNode_, seasonRNum)
    }

    private getReward(rank: number) {
        let rNum = 0
        const cfg = cfgHelper.getMiscData("pvp")[this._showArgs.type]
        for (const { min, max, num } of cfg.reward) {
            if (rank >= min && (max == -1 || rank <= max)) {
                rNum = num
                break
            }
        }
        return rNum
    }

    private getSeasonReward( rank: number) {
        let rNum = 0
        const cfg = cfgHelper.getMiscData("pvp")[this._showArgs.type]
        for (const { min, max, num } of cfg.seasonReward) {
            if (rank >= min && (max == -1 || rank <= max)) {
                rNum = num
                break
            }
        }
        return rNum
    }

    setReward(it: cc.Node, num: number) {
        it.Child("bk/num", cc.Label).string = num + ""
    }
    setEndTime() {
        this.dailyNode_.Child("endTime/lbl", cc.Label).setLocaleUpdate(() => ut.millisecondFormat(gameHelper.world.getNextDaySurpluTime(), "hh小时mm分钟"))
        this.seasonNode_.Child("endTime/lbl", cc.Label).setLocaleUpdate(() => ut.millisecondFormat(this.info.duration, `dd${assetsMgr.lang("common_timeUnit_day")}hh${assetsMgr.lang("common_timeUnit_hour")}`))
    }

    update(dt: number): void {
        this.setEndTime()
    }

}
